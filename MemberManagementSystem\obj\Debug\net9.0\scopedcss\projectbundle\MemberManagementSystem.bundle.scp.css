/* _content/MemberManagementSystem/Views/Shared/_Layout.cshtml.rz.scp.css */
/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-nzxkp9ylfx] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-nzxkp9ylfx] {
  color: #0077cc;
}

.btn-primary[b-nzxkp9ylfx] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-nzxkp9ylfx], .nav-pills .show > .nav-link[b-nzxkp9ylfx] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-nzxkp9ylfx] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-nzxkp9ylfx] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-nzxkp9ylfx] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-nzxkp9ylfx] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-nzxkp9ylfx] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
