using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MemberManagementSystem.Models
{
    public class AuditLog
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [Display(Name = "نوع العملية")]
        public string Action { get; set; } = string.Empty;

        [Required]
        [Display(Name = "اسم الجدول")]
        public string TableName { get; set; } = string.Empty;

        [Display(Name = "معرف السجل")]
        public string? RecordId { get; set; }

        [Display(Name = "القيم القديمة")]
        public string? OldValues { get; set; }

        [Display(Name = "القيم الجديدة")]
        public string? NewValues { get; set; }

        [Required]
        [Display(Name = "تاريخ العملية")]
        public DateTime Timestamp { get; set; } = DateTime.Now;

        [Required]
        [Display(Name = "المستخدم")]
        public string UserId { get; set; } = string.Empty;

        [Display(Name = "اسم المستخدم")]
        public string? UserName { get; set; }

        [Display(Name = "عنوان IP")]
        public string? IpAddress { get; set; }

        [Display(Name = "متصفح المستخدم")]
        public string? UserAgent { get; set; }

        // Foreign key for Member if the audit is related to a member
        [Display(Name = "معرف العضو")]
        public int? MemberId { get; set; }

        [ForeignKey("MemberId")]
        public virtual Member? Member { get; set; }
    }
}
