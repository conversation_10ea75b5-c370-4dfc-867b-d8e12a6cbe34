using OfficeOpenXml;
using OfficeOpenXml.Style;
using MemberManagementSystem.Models;
using System.Drawing;

namespace MemberManagementSystem.Services
{
    public class ExportService : IExportService
    {
        static ExportService()
        {
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        }
        public async Task<byte[]> ExportMembersToExcelAsync(List<Member> members, string title = "قائمة الأعضاء")
        {
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add(title);

            // Set RTL for Arabic text
            worksheet.View.RightToLeft = true;

            // Headers
            var headers = new string[]
            {
                "رقم العضوية", "الاسم الكامل", "تاريخ الميلاد", "العنوان",
                "رقم الهاتف", "البريد الإلكتروني", "الحالة",
                "تاريخ التقاعد", "تاريخ الوفاة", "تاريخ الإنشاء"
            };

            // Add headers
            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = headers[i];
                worksheet.Cells[1, i + 1].Style.Font.Bold = true;
                worksheet.Cells[1, i + 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
                worksheet.Cells[1, i + 1].Style.Fill.BackgroundColor.SetColor(Color.LightBlue);
                worksheet.Cells[1, i + 1].Style.Border.BorderAround(ExcelBorderStyle.Thin);
            }

            // Add data
            for (int i = 0; i < members.Count; i++)
            {
                var member = members[i];
                var row = i + 2;

                worksheet.Cells[row, 1].Value = member.MembershipNumber;
                worksheet.Cells[row, 2].Value = member.FullName;
                worksheet.Cells[row, 3].Value = member.DateOfBirth.ToString("yyyy-MM-dd");
                worksheet.Cells[row, 4].Value = member.Address;
                worksheet.Cells[row, 5].Value = member.PhoneNumber;
                worksheet.Cells[row, 6].Value = member.Email;
                worksheet.Cells[row, 7].Value = GetStatusText(member.Status);
                worksheet.Cells[row, 8].Value = member.RetirementDate?.ToString("yyyy-MM-dd");
                worksheet.Cells[row, 9].Value = member.DeathDate?.ToString("yyyy-MM-dd");
                worksheet.Cells[row, 10].Value = member.CreatedDate.ToString("yyyy-MM-dd");

                // Add borders
                for (int j = 1; j <= headers.Length; j++)
                {
                    worksheet.Cells[row, j].Style.Border.BorderAround(ExcelBorderStyle.Thin);
                }
            }

            // Auto-fit columns
            worksheet.Cells.AutoFitColumns();

            return await Task.FromResult(package.GetAsByteArray());
        }

        public async Task<byte[]> ExportRetiredMembersToExcelAsync(List<Member> members)
        {
            var retiredMembers = members.Where(m => m.Status == MemberStatus.Retired).ToList();
            return await ExportMembersToExcelAsync(retiredMembers, "قائمة المتقاعدين");
        }

        public async Task<byte[]> ExportDeceasedMembersToExcelAsync(List<Member> members)
        {
            var deceasedMembers = members.Where(m => m.Status == MemberStatus.Deceased).ToList();
            return await ExportMembersToExcelAsync(deceasedMembers, "قائمة الوفيات");
        }

        public async Task<byte[]> ExportAuditLogsToExcelAsync(List<AuditLog> auditLogs)
        {
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("سجل التدقيق");

            // Set RTL for Arabic text
            worksheet.View.RightToLeft = true;

            // Headers
            var headers = new string[]
            {
                "التاريخ", "العملية", "الجدول", "معرف السجل",
                "المستخدم", "عنوان IP", "الملاحظات"
            };

            // Add headers
            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = headers[i];
                worksheet.Cells[1, i + 1].Style.Font.Bold = true;
                worksheet.Cells[1, i + 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
                worksheet.Cells[1, i + 1].Style.Fill.BackgroundColor.SetColor(Color.LightGreen);
                worksheet.Cells[1, i + 1].Style.Border.BorderAround(ExcelBorderStyle.Thin);
            }

            // Add data
            for (int i = 0; i < auditLogs.Count; i++)
            {
                var log = auditLogs[i];
                var row = i + 2;

                worksheet.Cells[row, 1].Value = log.Timestamp.ToString("yyyy-MM-dd HH:mm:ss");
                worksheet.Cells[row, 2].Value = log.Action;
                worksheet.Cells[row, 3].Value = log.TableName;
                worksheet.Cells[row, 4].Value = log.RecordId;
                worksheet.Cells[row, 5].Value = log.UserName ?? log.UserId;
                worksheet.Cells[row, 6].Value = log.IpAddress;
                worksheet.Cells[row, 7].Value = log.Member?.FullName;

                // Add borders
                for (int j = 1; j <= headers.Length; j++)
                {
                    worksheet.Cells[row, j].Style.Border.BorderAround(ExcelBorderStyle.Thin);
                }
            }

            // Auto-fit columns
            worksheet.Cells.AutoFitColumns();

            return await Task.FromResult(package.GetAsByteArray());
        }

        private string GetStatusText(MemberStatus status)
        {
            return status switch
            {
                MemberStatus.Active => "نشط",
                MemberStatus.Retired => "متقاعد",
                MemberStatus.Deceased => "متوفى",
                _ => "غير محدد"
            };
        }
    }
}
