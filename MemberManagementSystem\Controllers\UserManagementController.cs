using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MemberManagementSystem.Models;
using MemberManagementSystem.Models.ViewModels;
using MemberManagementSystem.Services;

namespace MemberManagementSystem.Controllers
{
    [Authorize(Roles = "Admin")]
    public class UserManagementController : Controller
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly RoleManager<IdentityRole> _roleManager;
        private readonly IAuditService _auditService;

        public UserManagementController(
            UserManager<ApplicationUser> userManager,
            RoleManager<IdentityRole> roleManager,
            IAuditService auditService)
        {
            _userManager = userManager;
            _roleManager = roleManager;
            _auditService = auditService;
        }

        // GET: UserManagement
        public async Task<IActionResult> Index()
        {
            var users = await _userManager.Users.ToListAsync();
            var userViewModels = new List<UserListViewModel>();

            foreach (var user in users)
            {
                var roles = await _userManager.GetRolesAsync(user);
                userViewModels.Add(new UserListViewModel
                {
                    Id = user.Id,
                    UserName = user.UserName ?? "",
                    FullName = user.FullName,
                    Email = user.Email ?? "",
                    Role = roles.FirstOrDefault() ?? "No Role",
                    IsActive = user.IsActive,
                    CreatedDate = user.CreatedDate,
                    LastLoginDate = user.LastLoginDate
                });
            }

            return View(userViewModels);
        }

        // GET: UserManagement/Create
        public async Task<IActionResult> Create()
        {
            ViewBag.Roles = await _roleManager.Roles.Select(r => r.Name).ToListAsync();
            return View();
        }

        // POST: UserManagement/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(CreateUserViewModel model)
        {
            if (ModelState.IsValid)
            {
                var user = new ApplicationUser
                {
                    UserName = model.UserName,
                    Email = model.Email,
                    FullName = model.FullName,
                    Notes = model.Notes,
                    CreatedDate = DateTime.Now,
                    CreatedBy = User.Identity?.Name,
                    EmailConfirmed = true,
                    IsActive = true
                };

                var result = await _userManager.CreateAsync(user, model.Password);
                if (result.Succeeded)
                {
                    await _userManager.AddToRoleAsync(user, model.Role);

                    var currentUser = await _userManager.GetUserAsync(User);
                    await _auditService.LogAsync("Create", "User", user.Id, null, 
                        new { UserName = user.UserName, FullName = user.FullName, Role = model.Role }, 
                        currentUser?.Id ?? "", currentUser?.UserName);

                    TempData["SuccessMessage"] = "تم إنشاء المستخدم بنجاح.";
                    return RedirectToAction(nameof(Index));
                }

                foreach (var error in result.Errors)
                {
                    ModelState.AddModelError(string.Empty, error.Description);
                }
            }

            ViewBag.Roles = await _roleManager.Roles.Select(r => r.Name).ToListAsync();
            return View(model);
        }

        // GET: UserManagement/Edit/5
        public async Task<IActionResult> Edit(string id)
        {
            if (string.IsNullOrEmpty(id))
            {
                return NotFound();
            }

            var user = await _userManager.FindByIdAsync(id);
            if (user == null)
            {
                return NotFound();
            }

            var roles = await _userManager.GetRolesAsync(user);
            var model = new EditUserViewModel
            {
                Id = user.Id,
                UserName = user.UserName ?? "",
                FullName = user.FullName,
                Email = user.Email ?? "",
                Role = roles.FirstOrDefault() ?? "",
                IsActive = user.IsActive,
                Notes = user.Notes
            };

            ViewBag.Roles = await _roleManager.Roles.Select(r => r.Name).ToListAsync();
            return View(model);
        }

        // POST: UserManagement/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(EditUserViewModel model)
        {
            if (ModelState.IsValid)
            {
                var user = await _userManager.FindByIdAsync(model.Id);
                if (user == null)
                {
                    return NotFound();
                }

                var oldValues = new
                {
                    UserName = user.UserName,
                    FullName = user.FullName,
                    Email = user.Email,
                    IsActive = user.IsActive,
                    Notes = user.Notes
                };

                user.UserName = model.UserName;
                user.FullName = model.FullName;
                user.Email = model.Email;
                user.IsActive = model.IsActive;
                user.Notes = model.Notes;
                user.LastModifiedDate = DateTime.Now;
                user.LastModifiedBy = User.Identity?.Name;

                var result = await _userManager.UpdateAsync(user);
                if (result.Succeeded)
                {
                    // Update role
                    var currentRoles = await _userManager.GetRolesAsync(user);
                    await _userManager.RemoveFromRolesAsync(user, currentRoles);
                    await _userManager.AddToRoleAsync(user, model.Role);

                    var currentUser = await _userManager.GetUserAsync(User);
                    await _auditService.LogAsync("Update", "User", user.Id, oldValues,
                        new { UserName = user.UserName, FullName = user.FullName, Email = user.Email, 
                              IsActive = user.IsActive, Role = model.Role },
                        currentUser?.Id ?? "", currentUser?.UserName);

                    TempData["SuccessMessage"] = "تم تحديث بيانات المستخدم بنجاح.";
                    return RedirectToAction(nameof(Index));
                }

                foreach (var error in result.Errors)
                {
                    ModelState.AddModelError(string.Empty, error.Description);
                }
            }

            ViewBag.Roles = await _roleManager.Roles.Select(r => r.Name).ToListAsync();
            return View(model);
        }

        // POST: UserManagement/Delete/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Delete(string id)
        {
            var user = await _userManager.FindByIdAsync(id);
            if (user == null)
            {
                return NotFound();
            }

            // Don't allow deleting the current user
            var currentUser = await _userManager.GetUserAsync(User);
            if (currentUser?.Id == user.Id)
            {
                TempData["ErrorMessage"] = "لا يمكنك حذف حسابك الخاص.";
                return RedirectToAction(nameof(Index));
            }

            var result = await _userManager.DeleteAsync(user);
            if (result.Succeeded)
            {
                await _auditService.LogAsync("Delete", "User", user.Id,
                    new { UserName = user.UserName, FullName = user.FullName }, null,
                    currentUser?.Id ?? "", currentUser?.UserName);

                TempData["SuccessMessage"] = "تم حذف المستخدم بنجاح.";
            }
            else
            {
                TempData["ErrorMessage"] = "حدث خطأ أثناء حذف المستخدم.";
            }

            return RedirectToAction(nameof(Index));
        }

        // POST: UserManagement/ResetPassword/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ResetPassword(string id, string newPassword)
        {
            if (string.IsNullOrEmpty(newPassword) || newPassword.Length < 6)
            {
                TempData["ErrorMessage"] = "كلمة المرور يجب أن تكون على الأقل 6 أحرف.";
                return RedirectToAction(nameof(Index));
            }

            var user = await _userManager.FindByIdAsync(id);
            if (user == null)
            {
                return NotFound();
            }

            var token = await _userManager.GeneratePasswordResetTokenAsync(user);
            var result = await _userManager.ResetPasswordAsync(user, token, newPassword);

            if (result.Succeeded)
            {
                var currentUser = await _userManager.GetUserAsync(User);
                await _auditService.LogAsync("PasswordReset", "User", user.Id, null,
                    new { ResetTime = DateTime.Now }, currentUser?.Id ?? "", currentUser?.UserName);

                TempData["SuccessMessage"] = "تم إعادة تعيين كلمة المرور بنجاح.";
            }
            else
            {
                TempData["ErrorMessage"] = "حدث خطأ أثناء إعادة تعيين كلمة المرور.";
            }

            return RedirectToAction(nameof(Index));
        }
    }
}
