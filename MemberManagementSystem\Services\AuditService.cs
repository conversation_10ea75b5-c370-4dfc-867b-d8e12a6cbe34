using Microsoft.EntityFrameworkCore;
using MemberManagementSystem.Data;
using MemberManagementSystem.Models;
using System.Text.Json;

namespace MemberManagementSystem.Services
{
    public class AuditService : IAuditService
    {
        private readonly ApplicationDbContext _context;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public AuditService(ApplicationDbContext context, IHttpContextAccessor httpContextAccessor)
        {
            _context = context;
            _httpContextAccessor = httpContextAccessor;
        }

        public async Task LogAsync(string action, string tableName, string? recordId, object? oldValues, object? newValues, string userId, string? userName = null, int? memberId = null)
        {
            var httpContext = _httpContextAccessor.HttpContext;
            
            var auditLog = new AuditLog
            {
                Action = action,
                TableName = tableName,
                RecordId = recordId,
                OldValues = oldValues != null ? JsonSerializer.Serialize(oldValues) : null,
                NewValues = newValues != null ? JsonSerializer.Serialize(newValues) : null,
                UserId = userId,
                UserName = userName,
                MemberId = memberId,
                Timestamp = DateTime.Now,
                IpAddress = httpContext?.Connection?.RemoteIpAddress?.ToString(),
                UserAgent = httpContext?.Request?.Headers["User-Agent"].ToString()
            };

            _context.AuditLogs.Add(auditLog);
            await _context.SaveChangesAsync();
        }

        public async Task<List<AuditLog>> GetAuditLogsAsync(int? memberId = null, int pageNumber = 1, int pageSize = 50)
        {
            var query = _context.AuditLogs.AsQueryable();

            if (memberId.HasValue)
            {
                query = query.Where(a => a.MemberId == memberId.Value);
            }

            return await query
                .OrderByDescending(a => a.Timestamp)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .Include(a => a.Member)
                .ToListAsync();
        }

        public async Task<List<AuditLog>> GetUserActivityAsync(string userId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _context.AuditLogs.Where(a => a.UserId == userId);

            if (fromDate.HasValue)
            {
                query = query.Where(a => a.Timestamp >= fromDate.Value);
            }

            if (toDate.HasValue)
            {
                query = query.Where(a => a.Timestamp <= toDate.Value);
            }

            return await query
                .OrderByDescending(a => a.Timestamp)
                .Include(a => a.Member)
                .ToListAsync();
        }
    }
}
