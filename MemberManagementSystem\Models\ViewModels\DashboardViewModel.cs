namespace MemberManagementSystem.Models.ViewModels
{
    public class DashboardViewModel
    {
        public int TotalMembers { get; set; }
        public int ActiveMembers { get; set; }
        public int RetiredMembers { get; set; }
        public int DeceasedMembers { get; set; }
        public List<Member> RecentMembers { get; set; } = new List<Member>();
        public List<Member> RecentRetirements { get; set; } = new List<Member>();
        public List<Member> RecentDeaths { get; set; } = new List<Member>();
        public Dictionary<string, int> MonthlyStats { get; set; } = new Dictionary<string, int>();
    }
}
