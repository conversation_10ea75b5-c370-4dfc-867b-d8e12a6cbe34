﻿<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - نظام إدارة الأعضاء</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.rtl.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/MemberManagementSystem.styles.css" asp-append-version="true" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary shadow-sm">
            <div class="container-fluid">
                <a class="navbar-brand fw-bold" asp-controller="Dashboard" asp-action="Index">
                    <i class="bi bi-people-fill me-2"></i>نظام إدارة الأعضاء
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    @if (User.Identity!.IsAuthenticated)
                    {
                        <ul class="navbar-nav me-auto">
                            <li class="nav-item">
                                <a class="nav-link" asp-controller="Dashboard" asp-action="Index">
                                    <i class="bi bi-speedometer2 me-1"></i>لوحة التحكم
                                </a>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="bi bi-people me-1"></i>الأعضاء
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" asp-controller="Members" asp-action="Index">جميع الأعضاء</a></li>
                                    @if (User.IsInRole("Admin") || User.IsInRole("Employee"))
                                    {
                                        <li><a class="dropdown-item" asp-controller="Members" asp-action="Create">إضافة عضو جديد</a></li>
                                    }
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" asp-controller="Members" asp-action="Retired">المتقاعدون</a></li>
                                    <li><a class="dropdown-item" asp-controller="Members" asp-action="Deceased">الوفيات</a></li>
                                </ul>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="bi bi-file-earmark-text me-1"></i>التقارير
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" asp-controller="Reports" asp-action="MembershipReport">تقرير العضوية</a></li>
                                    <li><a class="dropdown-item" asp-controller="Reports" asp-action="RetirementReport">تقرير التقاعد</a></li>
                                    <li><a class="dropdown-item" asp-controller="Reports" asp-action="DeathReport">تقرير الوفيات</a></li>
                                    <li><a class="dropdown-item" asp-controller="Reports" asp-action="StatisticsReport">الإحصائيات</a></li>
                                    @if (User.IsInRole("Admin"))
                                    {
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" asp-controller="Reports" asp-action="AuditReport">سجل التدقيق</a></li>
                                    }
                                </ul>
                            </li>
                            @if (User.IsInRole("Admin"))
                            {
                                <li class="nav-item">
                                    <a class="nav-link" asp-controller="UserManagement" asp-action="Index">
                                        <i class="bi bi-gear me-1"></i>إدارة المستخدمين
                                    </a>
                                </li>
                            }
                        </ul>
                        <ul class="navbar-nav">
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="bi bi-person-circle me-1"></i>@User.Identity.Name
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li>
                                        <form asp-controller="Account" asp-action="Logout" method="post" class="d-inline">
                                            <button type="submit" class="dropdown-item">
                                                <i class="bi bi-box-arrow-right me-1"></i>تسجيل الخروج
                                            </button>
                                        </form>
                                    </li>
                                </ul>
                            </li>
                        </ul>
                    }
                </div>
            </div>
        </nav>
    </header>
    <div class="container-fluid mt-4">
        @if (TempData["SuccessMessage"] != null)
        {
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="bi bi-check-circle me-2"></i>@TempData["SuccessMessage"]
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        }
        @if (TempData["ErrorMessage"] != null)
        {
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="bi bi-exclamation-triangle me-2"></i>@TempData["ErrorMessage"]
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        }
        <main role="main" class="pb-3">
            @RenderBody()
        </main>
    </div>

    <footer class="bg-light border-top mt-5 py-3">
        <div class="container text-center text-muted">
            <p class="mb-0">&copy; 2025 - نظام إدارة الأعضاء المتقاعدين والوفيات - جميع الحقوق محفوظة</p>
        </div>
    </footer>
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
