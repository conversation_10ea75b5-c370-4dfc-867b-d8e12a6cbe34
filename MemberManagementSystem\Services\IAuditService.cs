using MemberManagementSystem.Models;

namespace MemberManagementSystem.Services
{
    public interface IAuditService
    {
        Task LogAsync(string action, string tableName, string? recordId, object? oldValues, object? newValues, string userId, string? userName = null, int? memberId = null);
        Task<List<AuditLog>> GetAuditLogsAsync(int? memberId = null, int pageNumber = 1, int pageSize = 50);
        Task<List<AuditLog>> GetUserActivityAsync(string userId, DateTime? fromDate = null, DateTime? toDate = null);
    }
}
