using MemberManagementSystem.Models;

namespace MemberManagementSystem.Services
{
    public interface IExportService
    {
        Task<byte[]> ExportMembersToExcelAsync(List<Member> members, string title = "قائمة الأعضاء");
        Task<byte[]> ExportRetiredMembersToExcelAsync(List<Member> members);
        Task<byte[]> ExportDeceasedMembersToExcelAsync(List<Member> members);
        Task<byte[]> ExportAuditLogsToExcelAsync(List<AuditLog> auditLogs);
    }
}
