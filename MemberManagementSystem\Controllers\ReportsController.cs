using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MemberManagementSystem.Data;
using MemberManagementSystem.Models;
using MemberManagementSystem.Services;

namespace MemberManagementSystem.Controllers
{
    [Authorize]
    public class ReportsController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly IExportService _exportService;
        private readonly IAuditService _auditService;

        public ReportsController(
            ApplicationDbContext context,
            IExportService exportService,
            IAuditService auditService)
        {
            _context = context;
            _exportService = exportService;
            _auditService = auditService;
        }

        // GET: Reports
        public IActionResult Index()
        {
            return View();
        }

        // GET: Reports/MembershipReport
        public async Task<IActionResult> MembershipReport(DateTime? fromDate, DateTime? toDate, MemberStatus? status)
        {
            var query = _context.Members.AsQueryable();

            if (fromDate.HasValue)
            {
                query = query.Where(m => m.CreatedDate >= fromDate.Value);
            }

            if (toDate.HasValue)
            {
                query = query.Where(m => m.CreatedDate <= toDate.Value);
            }

            if (status.HasValue)
            {
                query = query.Where(m => m.Status == status.Value);
            }

            var members = await query.OrderBy(m => m.FullName).ToListAsync();

            ViewBag.FromDate = fromDate;
            ViewBag.ToDate = toDate;
            ViewBag.Status = status;
            ViewBag.TotalCount = members.Count;

            return View(members);
        }

        // GET: Reports/RetirementReport
        public async Task<IActionResult> RetirementReport(DateTime? fromDate, DateTime? toDate)
        {
            var query = _context.Members.Where(m => m.Status == MemberStatus.Retired && m.RetirementDate.HasValue);

            if (fromDate.HasValue)
            {
                query = query.Where(m => m.RetirementDate >= fromDate.Value);
            }

            if (toDate.HasValue)
            {
                query = query.Where(m => m.RetirementDate <= toDate.Value);
            }

            var retiredMembers = await query.OrderByDescending(m => m.RetirementDate).ToListAsync();

            ViewBag.FromDate = fromDate;
            ViewBag.ToDate = toDate;
            ViewBag.TotalCount = retiredMembers.Count;

            return View(retiredMembers);
        }

        // GET: Reports/DeathReport
        public async Task<IActionResult> DeathReport(DateTime? fromDate, DateTime? toDate)
        {
            var query = _context.Members.Where(m => m.Status == MemberStatus.Deceased && m.DeathDate.HasValue);

            if (fromDate.HasValue)
            {
                query = query.Where(m => m.DeathDate >= fromDate.Value);
            }

            if (toDate.HasValue)
            {
                query = query.Where(m => m.DeathDate <= toDate.Value);
            }

            var deceasedMembers = await query.OrderByDescending(m => m.DeathDate).ToListAsync();

            ViewBag.FromDate = fromDate;
            ViewBag.ToDate = toDate;
            ViewBag.TotalCount = deceasedMembers.Count;

            return View(deceasedMembers);
        }

        // GET: Reports/AuditReport
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> AuditReport(DateTime? fromDate, DateTime? toDate, string? userId, string? action)
        {
            var auditLogs = await _auditService.GetAuditLogsAsync();
            
            if (fromDate.HasValue)
            {
                auditLogs = auditLogs.Where(a => a.Timestamp >= fromDate.Value).ToList();
            }

            if (toDate.HasValue)
            {
                auditLogs = auditLogs.Where(a => a.Timestamp <= toDate.Value).ToList();
            }

            if (!string.IsNullOrEmpty(userId))
            {
                auditLogs = auditLogs.Where(a => a.UserId == userId).ToList();
            }

            if (!string.IsNullOrEmpty(action))
            {
                auditLogs = auditLogs.Where(a => a.Action.Contains(action)).ToList();
            }

            ViewBag.FromDate = fromDate;
            ViewBag.ToDate = toDate;
            ViewBag.UserId = userId;
            ViewBag.Action = action;
            ViewBag.TotalCount = auditLogs.Count;

            return View(auditLogs);
        }

        // GET: Reports/StatisticsReport
        public async Task<IActionResult> StatisticsReport()
        {
            var currentYear = DateTime.Now.Year;
            var currentMonth = DateTime.Now.Month;

            var statistics = new
            {
                TotalMembers = await _context.Members.CountAsync(),
                ActiveMembers = await _context.Members.CountAsync(m => m.Status == MemberStatus.Active),
                RetiredMembers = await _context.Members.CountAsync(m => m.Status == MemberStatus.Retired),
                DeceasedMembers = await _context.Members.CountAsync(m => m.Status == MemberStatus.Deceased),
                
                // This year statistics
                NewMembersThisYear = await _context.Members.CountAsync(m => m.CreatedDate.Year == currentYear),
                RetirementsThisYear = await _context.Members.CountAsync(m => m.Status == MemberStatus.Retired && 
                    m.RetirementDate.HasValue && m.RetirementDate.Value.Year == currentYear),
                DeathsThisYear = await _context.Members.CountAsync(m => m.Status == MemberStatus.Deceased && 
                    m.DeathDate.HasValue && m.DeathDate.Value.Year == currentYear),
                
                // This month statistics
                NewMembersThisMonth = await _context.Members.CountAsync(m => m.CreatedDate.Year == currentYear && 
                    m.CreatedDate.Month == currentMonth),
                RetirementsThisMonth = await _context.Members.CountAsync(m => m.Status == MemberStatus.Retired && 
                    m.RetirementDate.HasValue && m.RetirementDate.Value.Year == currentYear && 
                    m.RetirementDate.Value.Month == currentMonth),
                DeathsThisMonth = await _context.Members.CountAsync(m => m.Status == MemberStatus.Deceased && 
                    m.DeathDate.HasValue && m.DeathDate.Value.Year == currentYear && 
                    m.DeathDate.Value.Month == currentMonth)
            };

            // Monthly breakdown for current year
            var monthlyData = new Dictionary<string, object>();
            for (int month = 1; month <= 12; month++)
            {
                var monthName = GetMonthName(month);
                monthlyData[monthName] = new
                {
                    NewMembers = await _context.Members.CountAsync(m => m.CreatedDate.Year == currentYear && 
                        m.CreatedDate.Month == month),
                    Retirements = await _context.Members.CountAsync(m => m.Status == MemberStatus.Retired && 
                        m.RetirementDate.HasValue && m.RetirementDate.Value.Year == currentYear && 
                        m.RetirementDate.Value.Month == month),
                    Deaths = await _context.Members.CountAsync(m => m.Status == MemberStatus.Deceased && 
                        m.DeathDate.HasValue && m.DeathDate.Value.Year == currentYear && 
                        m.DeathDate.Value.Month == month)
                };
            }

            ViewBag.Statistics = statistics;
            ViewBag.MonthlyData = monthlyData;
            ViewBag.CurrentYear = currentYear;

            return View();
        }

        // Export Reports
        public async Task<IActionResult> ExportMembershipReport(DateTime? fromDate, DateTime? toDate, MemberStatus? status)
        {
            var query = _context.Members.AsQueryable();

            if (fromDate.HasValue)
            {
                query = query.Where(m => m.CreatedDate >= fromDate.Value);
            }

            if (toDate.HasValue)
            {
                query = query.Where(m => m.CreatedDate <= toDate.Value);
            }

            if (status.HasValue)
            {
                query = query.Where(m => m.Status == status.Value);
            }

            var members = await query.OrderBy(m => m.FullName).ToListAsync();
            var fileContents = await _exportService.ExportMembersToExcelAsync(members, "تقرير العضوية");
            var fileName = $"تقرير_العضوية_{DateTime.Now:yyyyMMdd}.xlsx";

            return File(fileContents, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
        }

        public async Task<IActionResult> ExportRetirementReport(DateTime? fromDate, DateTime? toDate)
        {
            var query = _context.Members.Where(m => m.Status == MemberStatus.Retired && m.RetirementDate.HasValue);

            if (fromDate.HasValue)
            {
                query = query.Where(m => m.RetirementDate >= fromDate.Value);
            }

            if (toDate.HasValue)
            {
                query = query.Where(m => m.RetirementDate <= toDate.Value);
            }

            var retiredMembers = await query.OrderByDescending(m => m.RetirementDate).ToListAsync();
            var fileContents = await _exportService.ExportRetiredMembersToExcelAsync(retiredMembers);
            var fileName = $"تقرير_التقاعد_{DateTime.Now:yyyyMMdd}.xlsx";

            return File(fileContents, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
        }

        public async Task<IActionResult> ExportDeathReport(DateTime? fromDate, DateTime? toDate)
        {
            var query = _context.Members.Where(m => m.Status == MemberStatus.Deceased && m.DeathDate.HasValue);

            if (fromDate.HasValue)
            {
                query = query.Where(m => m.DeathDate >= fromDate.Value);
            }

            if (toDate.HasValue)
            {
                query = query.Where(m => m.DeathDate <= toDate.Value);
            }

            var deceasedMembers = await query.OrderByDescending(m => m.DeathDate).ToListAsync();
            var fileContents = await _exportService.ExportDeceasedMembersToExcelAsync(deceasedMembers);
            var fileName = $"تقرير_الوفيات_{DateTime.Now:yyyyMMdd}.xlsx";

            return File(fileContents, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
        }

        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> ExportAuditReport(DateTime? fromDate, DateTime? toDate, string? userId, string? action)
        {
            var auditLogs = await _auditService.GetAuditLogsAsync();
            
            if (fromDate.HasValue)
            {
                auditLogs = auditLogs.Where(a => a.Timestamp >= fromDate.Value).ToList();
            }

            if (toDate.HasValue)
            {
                auditLogs = auditLogs.Where(a => a.Timestamp <= toDate.Value).ToList();
            }

            if (!string.IsNullOrEmpty(userId))
            {
                auditLogs = auditLogs.Where(a => a.UserId == userId).ToList();
            }

            if (!string.IsNullOrEmpty(action))
            {
                auditLogs = auditLogs.Where(a => a.Action.Contains(action)).ToList();
            }

            var fileContents = await _exportService.ExportAuditLogsToExcelAsync(auditLogs);
            var fileName = $"تقرير_التدقيق_{DateTime.Now:yyyyMMdd}.xlsx";

            return File(fileContents, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
        }

        private string GetMonthName(int month)
        {
            return month switch
            {
                1 => "يناير",
                2 => "فبراير",
                3 => "مارس",
                4 => "أبريل",
                5 => "مايو",
                6 => "يونيو",
                7 => "يوليو",
                8 => "أغسطس",
                9 => "سبتمبر",
                10 => "أكتوبر",
                11 => "نوفمبر",
                12 => "ديسمبر",
                _ => "غير محدد"
            };
        }
    }
}
