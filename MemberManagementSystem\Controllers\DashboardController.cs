using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MemberManagementSystem.Data;
using MemberManagementSystem.Models;
using MemberManagementSystem.Models.ViewModels;

namespace MemberManagementSystem.Controllers
{
    [Authorize]
    public class DashboardController : Controller
    {
        private readonly ApplicationDbContext _context;

        public DashboardController(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<IActionResult> Index()
        {
            var viewModel = new DashboardViewModel
            {
                TotalMembers = await _context.Members.CountAsync(),
                ActiveMembers = await _context.Members.CountAsync(m => m.Status == MemberStatus.Active),
                RetiredMembers = await _context.Members.CountAsync(m => m.Status == MemberStatus.Retired),
                DeceasedMembers = await _context.Members.CountAsync(m => m.Status == MemberStatus.Deceased),
                RecentMembers = await _context.Members
                    .OrderByDescending(m => m.CreatedDate)
                    .Take(5)
                    .ToListAsync(),
                RecentRetirements = await _context.Members
                    .Where(m => m.Status == MemberStatus.Retired && m.RetirementDate.HasValue)
                    .OrderByDescending(m => m.RetirementDate)
                    .Take(5)
                    .ToListAsync(),
                RecentDeaths = await _context.Members
                    .Where(m => m.Status == MemberStatus.Deceased && m.DeathDate.HasValue)
                    .OrderByDescending(m => m.DeathDate)
                    .Take(5)
                    .ToListAsync()
            };

            // Get monthly statistics for the current year
            var currentYear = DateTime.Now.Year;
            var monthlyStats = await _context.Members
                .Where(m => m.CreatedDate.Year == currentYear)
                .GroupBy(m => m.CreatedDate.Month)
                .Select(g => new { Month = g.Key, Count = g.Count() })
                .ToListAsync();

            viewModel.MonthlyStats = monthlyStats.ToDictionary(
                x => GetMonthName(x.Month),
                x => x.Count
            );

            return View(viewModel);
        }

        private string GetMonthName(int month)
        {
            return month switch
            {
                1 => "يناير",
                2 => "فبراير",
                3 => "مارس",
                4 => "أبريل",
                5 => "مايو",
                6 => "يونيو",
                7 => "يوليو",
                8 => "أغسطس",
                9 => "سبتمبر",
                10 => "أكتوبر",
                11 => "نوفمبر",
                12 => "ديسمبر",
                _ => "غير محدد"
            };
        }
    }
}
