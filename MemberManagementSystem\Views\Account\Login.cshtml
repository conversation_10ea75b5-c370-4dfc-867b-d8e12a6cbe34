@model MemberManagementSystem.Models.ViewModels.LoginViewModel
@{
    ViewData["Title"] = "تسجيل الدخول";
    Layout = "~/Views/Shared/_LoginLayout.cshtml";
}

<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card shadow">
            <div class="card-body p-4">
                <div class="text-center mb-4">
                    <i class="bi bi-people-fill text-primary" style="font-size: 3rem;"></i>
                    <h3 class="mt-2">نظام إدارة الأعضاء</h3>
                    <p class="text-muted">يرجى تسجيل الدخول للمتابعة</p>
                </div>

                <form asp-action="Login" method="post">
                    <div asp-validation-summary="All" class="text-danger mb-3"></div>
                    
                    <div class="mb-3">
                        <label asp-for="UserName" class="form-label">
                            <i class="bi bi-person me-1"></i>@Html.DisplayNameFor(m => m.UserName)
                        </label>
                        <input asp-for="UserName" class="form-control" placeholder="أدخل اسم المستخدم" />
                        <span asp-validation-for="UserName" class="text-danger"></span>
                    </div>

                    <div class="mb-3">
                        <label asp-for="Password" class="form-label">
                            <i class="bi bi-lock me-1"></i>@Html.DisplayNameFor(m => m.Password)
                        </label>
                        <input asp-for="Password" class="form-control" placeholder="أدخل كلمة المرور" />
                        <span asp-validation-for="Password" class="text-danger"></span>
                    </div>

                    <div class="mb-3 form-check">
                        <input asp-for="RememberMe" class="form-check-input" />
                        <label asp-for="RememberMe" class="form-check-label">
                            @Html.DisplayNameFor(m => m.RememberMe)
                        </label>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="bi bi-box-arrow-in-right me-1"></i>تسجيل الدخول
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <div class="text-center mt-3">
            <small class="text-muted">
                للحصول على حساب جديد، يرجى الاتصال بمدير النظام
            </small>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
