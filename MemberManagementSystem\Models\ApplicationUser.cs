using Microsoft.AspNetCore.Identity;
using System.ComponentModel.DataAnnotations;

namespace MemberManagementSystem.Models
{
    public class ApplicationUser : IdentityUser
    {
        [Required]
        [StringLength(100)]
        [Display(Name = "الاسم الكامل")]
        public string FullName { get; set; } = string.Empty;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [Display(Name = "آخر تسجيل دخول")]
        public DateTime? LastLoginDate { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [StringLength(500)]
        [Display(Name = "ملاحظات")]
        public string? Notes { get; set; }

        [Display(Name = "منشئ الحساب")]
        public string? CreatedBy { get; set; }

        [Display(Name = "تاريخ آخر تحديث")]
        public DateTime? LastModifiedDate { get; set; }

        [Display(Name = "آخر من عدل الحساب")]
        public string? LastModifiedBy { get; set; }
    }
}
