using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MemberManagementSystem.Data;
using MemberManagementSystem.Models;
using MemberManagementSystem.Services;

namespace MemberManagementSystem.Controllers
{
    [Authorize]
    public class MembersController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IAuditService _auditService;
        private readonly IExportService _exportService;

        public MembersController(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager,
            IAuditService auditService,
            IExportService exportService)
        {
            _context = context;
            _userManager = userManager;
            _auditService = auditService;
            _exportService = exportService;
        }

        // GET: Members
        public async Task<IActionResult> Index(string? searchString, MemberStatus? status, int page = 1)
        {
            const int pageSize = 20;
            var query = _context.Members.AsQueryable();

            if (!string.IsNullOrEmpty(searchString))
            {
                query = query.Where(m => m.FullName.Contains(searchString) || 
                                        m.MembershipNumber.Contains(searchString));
            }

            if (status.HasValue)
            {
                query = query.Where(m => m.Status == status.Value);
            }

            var totalItems = await query.CountAsync();
            var members = await query
                .OrderByDescending(m => m.CreatedDate)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            ViewBag.SearchString = searchString;
            ViewBag.Status = status;
            ViewBag.CurrentPage = page;
            ViewBag.TotalPages = (int)Math.Ceiling(totalItems / (double)pageSize);

            return View(members);
        }

        // GET: Members/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var member = await _context.Members
                .FirstOrDefaultAsync(m => m.Id == id);
            if (member == null)
            {
                return NotFound();
            }

            return View(member);
        }

        // GET: Members/Create
        [Authorize(Roles = "Admin,Employee")]
        public IActionResult Create()
        {
            return View();
        }

        // POST: Members/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize(Roles = "Admin,Employee")]
        public async Task<IActionResult> Create([Bind("FullName,MembershipNumber,DateOfBirth,Address,PhoneNumber,Email,Status,RetirementDate,DeathDate,Notes")] Member member)
        {
            if (ModelState.IsValid)
            {
                var user = await _userManager.GetUserAsync(User);
                member.CreatedBy = user?.UserName;
                member.CreatedDate = DateTime.Now;

                _context.Add(member);
                await _context.SaveChangesAsync();

                // Log the creation
                await _auditService.LogAsync("Create", "Member", member.Id.ToString(), 
                    null, member, user?.Id ?? "", user?.UserName, member.Id);

                TempData["SuccessMessage"] = "تم إضافة العضو بنجاح.";
                return RedirectToAction(nameof(Index));
            }
            return View(member);
        }

        // GET: Members/Edit/5
        [Authorize(Roles = "Admin,Employee")]
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var member = await _context.Members.FindAsync(id);
            if (member == null)
            {
                return NotFound();
            }
            return View(member);
        }

        // POST: Members/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize(Roles = "Admin,Employee")]
        public async Task<IActionResult> Edit(int id, [Bind("Id,FullName,MembershipNumber,DateOfBirth,Address,PhoneNumber,Email,Status,RetirementDate,DeathDate,Notes,CreatedDate,CreatedBy")] Member member)
        {
            if (id != member.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    var originalMember = await _context.Members.AsNoTracking().FirstOrDefaultAsync(m => m.Id == id);
                    var user = await _userManager.GetUserAsync(User);
                    
                    member.LastModifiedBy = user?.UserName;
                    member.LastModifiedDate = DateTime.Now;

                    _context.Update(member);
                    await _context.SaveChangesAsync();

                    // Log the update
                    await _auditService.LogAsync("Update", "Member", member.Id.ToString(),
                        originalMember, member, user?.Id ?? "", user?.UserName, member.Id);

                    TempData["SuccessMessage"] = "تم تحديث بيانات العضو بنجاح.";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!MemberExists(member.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }
            return View(member);
        }

        // GET: Members/Delete/5
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var member = await _context.Members
                .FirstOrDefaultAsync(m => m.Id == id);
            if (member == null)
            {
                return NotFound();
            }

            return View(member);
        }

        // POST: Members/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var member = await _context.Members.FindAsync(id);
            if (member != null)
            {
                var user = await _userManager.GetUserAsync(User);
                
                _context.Members.Remove(member);
                await _context.SaveChangesAsync();

                // Log the deletion
                await _auditService.LogAsync("Delete", "Member", member.Id.ToString(),
                    member, null, user?.Id ?? "", user?.UserName, member.Id);

                TempData["SuccessMessage"] = "تم حذف العضو بنجاح.";
            }

            return RedirectToAction(nameof(Index));
        }

        // GET: Members/Retired
        public async Task<IActionResult> Retired()
        {
            var retiredMembers = await _context.Members
                .Where(m => m.Status == MemberStatus.Retired)
                .OrderByDescending(m => m.RetirementDate)
                .ToListAsync();

            return View(retiredMembers);
        }

        // GET: Members/Deceased
        public async Task<IActionResult> Deceased()
        {
            var deceasedMembers = await _context.Members
                .Where(m => m.Status == MemberStatus.Deceased)
                .OrderByDescending(m => m.DeathDate)
                .ToListAsync();

            return View(deceasedMembers);
        }

        // Export to Excel
        public async Task<IActionResult> ExportToExcel(MemberStatus? status = null)
        {
            var query = _context.Members.AsQueryable();
            
            if (status.HasValue)
            {
                query = query.Where(m => m.Status == status.Value);
            }

            var members = await query.ToListAsync();
            
            byte[] fileContents;
            string fileName;

            switch (status)
            {
                case MemberStatus.Retired:
                    fileContents = await _exportService.ExportRetiredMembersToExcelAsync(members);
                    fileName = $"المتقاعدين_{DateTime.Now:yyyyMMdd}.xlsx";
                    break;
                case MemberStatus.Deceased:
                    fileContents = await _exportService.ExportDeceasedMembersToExcelAsync(members);
                    fileName = $"الوفيات_{DateTime.Now:yyyyMMdd}.xlsx";
                    break;
                default:
                    fileContents = await _exportService.ExportMembersToExcelAsync(members);
                    fileName = $"جميع_الأعضاء_{DateTime.Now:yyyyMMdd}.xlsx";
                    break;
            }

            return File(fileContents, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
        }

        private bool MemberExists(int id)
        {
            return _context.Members.Any(e => e.Id == id);
        }
    }
}
